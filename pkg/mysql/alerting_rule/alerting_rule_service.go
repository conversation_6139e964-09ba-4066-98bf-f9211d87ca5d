package alerting_rule

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	v1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	vmv1beta1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/vm/v1beta1"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/model"
)

var timeTemplate = "2006-01-02T15:04:05Z" // time.Time转换为string的模板格式

func Insert(alertingRuleRecord model.AlertingRule) (err error) {
	alertingRuleRecord.UpdateTime = time.Now() // 无论数据值是否更新，都将update_time更新为当前时间
	result := mysql.Conn.Create(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func Update(alertingRuleRecord model.AlertingRule) (err error) {
	alertingRuleRecord.UpdateTime = time.Now() // 无论数据值是否更新，都将update_time更新为当前时间
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_id = ?", alertingRuleRecord.AlertingRuleID).
		Where("new_instance_id = ?", alertingRuleRecord.NewInstanceID).
		Where("is_delete = ?", 0).
		Updates(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func QueryByName(alertingRuleName string, newInstanceId string) (alertingRule v1.AlertingRule, err error) {
	var alertingRuleRecord model.AlertingRule
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_name = ?", alertingRuleName).
		Where("new_instance_id = ?", newInstanceId).
		Find(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func QueryById(alertingRuleId string, newInstanceId string) (alertingRuleRecord model.AlertingRule, err error) {
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_id = ?", alertingRuleId).
		Where("new_instance_id = ?", newInstanceId).
		Where("is_delete = ?", 0).
		Find(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func CountById(newInstanceId string) (count int, err error) {
	db := mysql.Conn.Model(&model.AlertingRule{}).
		Where("new_instance_id = ?", newInstanceId).
		Where("is_delete = ?", 0)
	var totalCount int
	db.Count(&totalCount)
	if db.Error != nil {
		err = db.Error
		return
	}
	return totalCount, nil
}

func Delete(alertingRuleId string, newInstanceId string) (err error) {
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_id = ?", alertingRuleId).
		Where("new_instance_id = ?", newInstanceId).
		Update("is_delete", 1)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func QueryLimitByPageSize(alertingRuleName string, newInstanceId string, pageSize int64, pageNo int64) (int, []model.AlertingRule, error) {
	var alertingRuleRecords []model.AlertingRule
	db := mysql.Conn.Model(&model.AlertingRule{}).
		Where(fmt.Sprintf("alerting_rule_name like %q", ("%"+alertingRuleName+"%"))).
		Where("new_instance_id = ?", newInstanceId).
		Where("is_delete = ?", 0)
	var totalCount int
	db.Count(&totalCount)
	db.Offset((pageNo - 1) * pageSize).Limit(pageSize).Order("update_time desc").Find(&alertingRuleRecords)
	err := db.Error

	return totalCount, alertingRuleRecords, err
}

func BatchDelete(newInstanceId string, accountId string) (err error) {
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("bce_account_id = ?", accountId).
		Where("new_instance_id = ?", newInstanceId).
		Update("is_delete", 1)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// BatchUpdateEnable updates the enable status for multiple alerting rules
func BatchUpdateEnable(alertingRuleIds []string, instanceID string, accountID string, enable bool) error {
	if len(alertingRuleIds) == 0 {
		return nil
	}

	enableStr := strconv.FormatBool(enable)
	now := time.Now()

	// 批量查询所有需要更新的记录
	var alertingRuleRecords []model.AlertingRule
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_id IN ?", alertingRuleIds).
		Where("new_instance_id = ?", instanceID).
		Where("bce_account_id = ?", accountID).
		Where("is_delete = ?", 0).
		Find(&alertingRuleRecords)

	if result.Error != nil {
		return fmt.Errorf("failed to batch query alerting rules: %v", result.Error)
	}

	if len(alertingRuleRecords) == 0 {
		return fmt.Errorf("no alerting rules found for the given criteria")
	}

	// 准备批量更新的数据
	var updateRecords []model.AlertingRule
	for _, record := range alertingRuleRecords {
		// Parse the VMRule from JSON
		var vmRule vmv1beta1.VMRule
		err := json.Unmarshal([]byte(record.AlertingRuleDetail), &vmRule)
		if err != nil {
			return fmt.Errorf("failed to unmarshal VMRule for alerting rule %s: %v", record.AlertingRuleID, err)
		}

		// Update the enable label
		if vmRule.Labels == nil {
			vmRule.Labels = make(map[string]string)
		}
		vmRule.Labels[cpromv1.EnableLabel] = enableStr

		// Marshal back to JSON
		vmRuleBytes, err := json.Marshal(&vmRule)
		if err != nil {
			return fmt.Errorf("failed to marshal VMRule for alerting rule %s: %v", record.AlertingRuleID, err)
		}

		// 准备更新记录
		record.AlertingRuleDetail = string(vmRuleBytes)
		record.UpdateTime = now
		updateRecords = append(updateRecords, record)
	}

	// 使用事务进行批量更新
	tx := mysql.Conn.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	for _, record := range updateRecords {
		updateResult := tx.Model(&model.AlertingRule{}).
			Where("id = ?", record.ID).
			Updates(map[string]interface{}{
				"alerting_rule_detail": record.AlertingRuleDetail,
				"update_time":          record.UpdateTime,
			})

		if updateResult.Error != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update alerting rule %s: %v", record.AlertingRuleID, updateResult.Error)
		}
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}

/*
func ConvertModel2Protocol(alertingRuleRecord model.AlertingRule) (alertingRule v1.AlertingRule) {

	alertingRule.MonitorInstanceId = alertingRuleRecord.NewInstanceID
	alertingRule.AlertingRuleID = alertingRuleRecord.AlertingRuleID
	alertingRule.AlertingRuleName = alertingRuleRecord.AlertingRuleName
	alertingRule.Expr = alertingRuleRecord.Expr
	alertingRule.Duration = alertingRuleRecord.For

	alertingRule.Severity = ""  // todo
	alertingRule.Description = ""  // todo

	isEnable := true
	if alertingRuleRecord.Enable == 0 {
		isEnable = false
	}
	alertingRule.IsEnabled = isEnable
	alertingRule.Operator = alertingRuleRecord.Operator

	err := json.Unmarshal([]byte(alertingRuleRecord.Labels), &alertingRule.Labels)
	if err != nil {
		zap.S().Errorf("JsonUnmarshal for alertingRuleRecord.Labels failed. errorInfo=[%v]", err.Error())
	}
	err = json.Unmarshal([]byte(alertingRuleRecord.Annotations), &alertingRule.Annotations)
	if err != nil {
		zap.S().Errorf("JsonUnmarshal for alertingRuleRecord.Annotations failed. errorInfo=[%v]", err.Error())
	}
	alertingRule.UpdateTime = int(alertingRuleRecord.UpdateTime.Local().Unix())  // 从time.Time -> 时间戳
	return
}
*/
