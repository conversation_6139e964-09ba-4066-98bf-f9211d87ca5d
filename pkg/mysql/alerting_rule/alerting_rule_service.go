package alerting_rule

import (
	"fmt"
	v1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/model"
	"time"
)

var timeTemplate = "2006-01-02T15:04:05Z" // time.Time转换为string的模板格式

func Insert(alertingRuleRecord model.AlertingRule) (err error) {
	alertingRuleRecord.UpdateTime = time.Now() // 无论数据值是否更新，都将update_time更新为当前时间
	result := mysql.Conn.Create(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func Update(alertingRuleRecord model.AlertingRule) (err error) {
	alertingRuleRecord.UpdateTime = time.Now() // 无论数据值是否更新，都将update_time更新为当前时间
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_id = ?", alertingRuleRecord.AlertingRuleID).
		Where("new_instance_id = ?", alertingRuleRecord.NewInstanceID).
		Where("is_delete = ?", 0).
		Updates(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func QueryByName(alertingRuleName string, newInstanceId string) (alertingRule v1.AlertingRule, err error) {
	var alertingRuleRecord model.AlertingRule
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_name = ?", alertingRuleName).
		Where("new_instance_id = ?", newInstanceId).
		Find(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func QueryById(alertingRuleId string, newInstanceId string) (alertingRuleRecord model.AlertingRule, err error) {
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_id = ?", alertingRuleId).
		Where("new_instance_id = ?", newInstanceId).
		Where("is_delete = ?", 0).
		Find(&alertingRuleRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func CountById(newInstanceId string) (count int, err error) {
	db := mysql.Conn.Model(&model.AlertingRule{}).
		Where("new_instance_id = ?", newInstanceId).
		Where("is_delete = ?", 0)
	var totalCount int
	db.Count(&totalCount)
	if db.Error != nil {
		err = db.Error
		return
	}
	return totalCount, nil
}

func Delete(alertingRuleId string, newInstanceId string) (err error) {
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("alerting_rule_id = ?", alertingRuleId).
		Where("new_instance_id = ?", newInstanceId).
		Update("is_delete", 1)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

func QueryLimitByPageSize(alertingRuleName string, newInstanceId string, pageSize int64, pageNo int64) (int, []model.AlertingRule, error) {
	var alertingRuleRecords []model.AlertingRule
	db := mysql.Conn.Model(&model.AlertingRule{}).
		Where(fmt.Sprintf("alerting_rule_name like %q", ("%"+alertingRuleName+"%"))).
		Where("new_instance_id = ?", newInstanceId).
		Where("is_delete = ?", 0)
	var totalCount int
	db.Count(&totalCount)
	db.Offset((pageNo - 1) * pageSize).Limit(pageSize).Order("update_time desc").Find(&alertingRuleRecords)
	err := db.Error

	return totalCount, alertingRuleRecords, err
}

func BatchDelete(newInstanceId string, accountId string) (err error) {
	result := mysql.Conn.Model(&model.AlertingRule{}).
		Where("bce_account_id = ?", accountId).
		Where("new_instance_id = ?", newInstanceId).
		Update("is_delete", 1)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

/*
func ConvertModel2Protocol(alertingRuleRecord model.AlertingRule) (alertingRule v1.AlertingRule) {

	alertingRule.MonitorInstanceId = alertingRuleRecord.NewInstanceID
	alertingRule.AlertingRuleID = alertingRuleRecord.AlertingRuleID
	alertingRule.AlertingRuleName = alertingRuleRecord.AlertingRuleName
	alertingRule.Expr = alertingRuleRecord.Expr
	alertingRule.Duration = alertingRuleRecord.For

	alertingRule.Severity = ""  // todo
	alertingRule.Description = ""  // todo

	isEnable := true
	if alertingRuleRecord.Enable == 0 {
		isEnable = false
	}
	alertingRule.IsEnabled = isEnable
	alertingRule.Operator = alertingRuleRecord.Operator

	err := json.Unmarshal([]byte(alertingRuleRecord.Labels), &alertingRule.Labels)
	if err != nil {
		zap.S().Errorf("JsonUnmarshal for alertingRuleRecord.Labels failed. errorInfo=[%v]", err.Error())
	}
	err = json.Unmarshal([]byte(alertingRuleRecord.Annotations), &alertingRule.Annotations)
	if err != nil {
		zap.S().Errorf("JsonUnmarshal for alertingRuleRecord.Annotations failed. errorInfo=[%v]", err.Error())
	}
	alertingRule.UpdateTime = int(alertingRuleRecord.UpdateTime.Local().Unix())  // 从time.Time -> 时间戳
	return
}
*/
